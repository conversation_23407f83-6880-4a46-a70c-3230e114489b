

#include "ipc_decoder.h"
#include "ipc/ipc.h"
#include "slc_detect.h"
#include "slc_flow.h"
#include "slc_logger.h"
#include "slc_rtsp.h"
#include "slc_sip.h"
#include "slc_tcp.h"
#include <cstdio>
#include <cstring>
#include <netinet/in.h>

SlcProtoRegBase* SlcProtoRegBase::modules_ = NULL;

std::map<int, FuncPair>& getRegDissectCallbackMap() {
  static std::map<int, FuncPair> g_RegDissectCallbackMap = {};
  return g_RegDissectCallbackMap;
}

slc::Logger global_logger;

ipc_decoder::ipc_decoder(ipc_cfg* ipc_conf) {
  this->cfg_ = ipc_conf;
  ipc_logger.SetLogLevel(cfg_->GetLogLevel());
  global_logger.SetLogLevel(cfg_->GetLogLevel());
  SlcProtoRegBase::registerAllProtocol();
  new_stream_fn = cfg_->new_stream_fn;
  new_nalu_fn = cfg_->new_nalu_fn;
  user_ = cfg_->userdata;
}

ipc_decoder::~ipc_decoder() { delete this->cfg_; }

void ipc_decoder::onProcessPkt(const uint8_t* buff, int len) {
  int lSts = 0;
  SLCPARSER->parser(buff, len, this);
  if (lSts < 0) {
    return;
  }
}

ipc_stream_info* ipc_decoder::getStreamInfo(uint64_t stream_id) {
  auto search = this->nalu_stream_map.find(stream_id);
  if (search != this->nalu_stream_map.end()) {
    return search->second.get();
  }
  return nullptr;
}

flow_info* ipc_decoder::findCreateFlow(five_tuple* key) {
  // 根据IP版本确定实际使用的IP地址长度
  size_t ip_len = (key->ip_version == 6) ? 16 : 4;
  std::string ip_dst((char*)&key->ip_dst, ip_len);
  std::string ip_src((char*)&key->ip_src, ip_len);

  // 标准化流标识符：确保同一条流的正反向包使用相同的哈希键
  // 通过比较IP地址和端口，将较小的作为第一个参数，较大的作为第二个参数
  std::string ip1, ip2;
  uint16_t port1, port2;

  if (ip_src < ip_dst || (ip_src == ip_dst && key->port_src < key->port_dst)) {
    ip1 = ip_src;
    ip2 = ip_dst;
    port1 = key->port_src;
    port2 = key->port_dst;
  } else {
    ip1 = ip_dst;
    ip2 = ip_src;
    port1 = key->port_dst;
    port2 = key->port_src;
  }

  Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t> tuple_key(
      ip1, ip2, port1, port2, key->ip_version, key->proto);
  auto it = flow_map.find(tuple_key);
  if (it == flow_map.end()) {
    auto p = std::make_shared<flow_info>();
    if (p == nullptr) {
      ipc_logger.Debug("std::make_shared<flow_info>()");
      return NULL;
    }
    memcpy(&p->tuple, key, sizeof(five_tuple));
    flow_map[tuple_key] = p;
  }
  return flow_map[tuple_key].get();
}
void ipc_decoder::updateCapabilitynum(std::shared_ptr<ipc_stream_info> p){
  printf("updateCapabilitynum stream_id: %lu\n", p->stream_id);
  if (p->capability_info.device_desc[0] != 0) {
    p->capability = p->capability | IPC_CAPABILITY_DEVICEID;
  }
  if (p->capability_info.sample_rate != 0) {
    p->capability = p->capability | IPC_CAPABILITY_SAMPLE_RATE;
  }
  if (p->capability_info.encodeType_video != IPC_AVC_NONE) {
    p->capability = p->capability | IPC_CAPABILITY_ADVANCED_VIDEO_CODING;
  }
  if (p->capability_info.encodeType_audio != IPC_AAC_NONE) {
    p->capability = p->capability | IPC_CAPABILITY_ADVANCED_AUDIO_CODING;
  }
  p->capability = p->capability | IPC_CAPABILITY_IPC_PROTO_TYPE;
}
void ipc_decoder::setIpcStreamCapability(uint64_t stream_id, struct ipc_capability_t& capability_info) {
  printf("set IpcStreamCapability stream_id: %lu\n", stream_id);
  auto it = nalu_stream_map.find(stream_id);
  if (it == nalu_stream_map.end()) {
    return;
  }
  memcpy(&nalu_stream_map[stream_id]->capability_info, &capability_info, sizeof(struct ipc_capability_t));
  if (capability_info.device_desc[0] != 0) {
    nalu_stream_map[stream_id]->capability = nalu_stream_map[stream_id]->capability | IPC_CAPABILITY_DEVICEID;
  }
  if (capability_info.sample_rate != 0) {
    nalu_stream_map[stream_id]->capability = nalu_stream_map[stream_id]->capability | IPC_CAPABILITY_SAMPLE_RATE;
  }
  if (capability_info.encodeType_video != IPC_AVC_NONE) {
    nalu_stream_map[stream_id]->capability = nalu_stream_map[stream_id]->capability | IPC_CAPABILITY_ADVANCED_VIDEO_CODING;
  }
  if (capability_info.encodeType_audio != IPC_AAC_NONE) {
    nalu_stream_map[stream_id]->capability = nalu_stream_map[stream_id]->capability | IPC_CAPABILITY_ADVANCED_AUDIO_CODING;
  }

  nalu_stream_map[stream_id]->capability = nalu_stream_map[stream_id]->capability | IPC_CAPABILITY_IPC_PROTO_TYPE;
}
void ipc_decoder::onNewIpcStreamFunc(std::shared_ptr<ipc_stream_info> p) {
  if (p->seq == 0) {
    updateCapabilitynum(p);
    new_stream_fn(p->stream_id, user_);
    p->seq++;
  }
}

void ipc_decoder::creatIpcStreamFromSip(void* user) {
  if (!user) {
    return;
  }
  SipStream* stream = static_cast<SipStream*>(user);

  flow_info flow;
  memcpy(&flow.tuple, &stream->tuple, sizeof stream->tuple);
  flow.tuple.port_dst = stream->sdp_.rtp_dst_port;
  flow.tuple.port_src = stream->sdp_.rtp_src_port;

  auto it = findCreatIpcStrem(&flow, stream->ssrc);

  struct ipc_capability_t capability;
  if (!stream->sdp_.user_agent.empty()) {
    strcpy(capability.device_desc, stream->sdp_.user_agent.c_str());
  }
  if (0 != stream->sdp_.sample_rate_) {
    capability.sample_rate = stream->sdp_.sample_rate_;
  }
  capability.proto_type = IPC_PROTOCOL_SIP;
  setIpcStreamCapability(it->stream_id, capability);
  if (global_logger.logLevel == LogLevel::Warning) {
    printf("creatIpcStreamFromSip ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d "
           "%d ssrc: %x\n",
           stream->tuple.ip_src[0], stream->tuple.ip_src[1],
           stream->tuple.ip_src[2], stream->tuple.ip_src[3],
           stream->tuple.ip_dst[0], stream->tuple.ip_dst[1],
           stream->tuple.ip_dst[2], stream->tuple.ip_dst[3],
           ntohs(stream->tuple.port_src), ntohs(stream->tuple.port_dst),stream->ssrc);
  }
  // onNewIpcStreamFunc(it);
}

//通过信令创建icp_stream
void ipc_decoder::creatIpcStreamFromRTSP(void* rtsp_stream, void* rtp_stream, uint32_t ssrc, RtpMediaType MediaType, uint8_t* sps,
    int sps_len, uint8_t* pps, int pps_len) {
  if (rtsp_stream == NULL) {
    return;
  }

  RtspStream* stream = static_cast<RtspStream*>(rtsp_stream);
  RtpStream*  rtpstream = static_cast<RtpStream*>(rtp_stream);

  auto p = findCreatIpcStrem(rtpstream, ssrc);

  if (sps_len > 0) {
    p->setSPS((uint8_t*)sps, sps_len);
  }
  if (stream->sps_len_ > 0) {
    p->setSPS((uint8_t*)stream->sps_, stream->sps_len_);
  }
  if (pps_len > 0) {
    p->setPPS((uint8_t*)pps, pps_len);
  }
  if (stream->pps_len_ > 0) {
    p->setPPS((uint8_t*)stream->pps_, stream->pps_len_);
  }

  struct ipc_capability_t capability;
  memset(&capability, 0,sizeof capability);
  if (MediaType == RtpMediaType::video_h264) {
    capability.encodeType_video = IPC_AVC_H264;
  } else if (MediaType == RtpMediaType::video_h263) {
    capability.encodeType_video = IPC_AVC_H263;
  }else if (MediaType == RtpMediaType::video_h265) {
    capability.encodeType_video = IPC_AVC_H265;
  }else if (MediaType == RtpMediaType::video_h261) {
    capability.encodeType_video = IPC_AVC_NONE;
  }else if(MediaType== RtpMediaType ::audio ){
    capability.encodeType_audio = IPC_AAC_NONE;
  } else if (MediaType == RtpMediaType::audio_g711) {
    capability.encodeType_audio = IPC_AAC_G711a;
  } else if (MediaType == RtpMediaType::audio_g729) {
    capability.encodeType_audio = IPC_AAC_G729;
  } else {
    capability.encodeType_video = IPC_AVC_NONE;
  }
  capability.proto_type = IPC_PROTOCOL_RTSP;
  if (0 != strlen(stream->sdp_.info.s_name)) {
    strcpy(capability.device_desc, stream->sdp_.info.s_name);
  }
  if (0 != stream->sdp_.sample_rate_) {
    capability.sample_rate = stream->sdp_.sample_rate_;
  }
  setIpcStreamCapability(p->stream_id, capability);
  if (global_logger.logLevel == LogLevel::Warning) {
    printf("creatIpcStreamFromRTSP ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d "
           "%d ssrc: %x\n",
           stream->tuple.ip_src[0], stream->tuple.ip_src[1],
           stream->tuple.ip_src[2], stream->tuple.ip_src[3],
           stream->tuple.ip_dst[0], stream->tuple.ip_dst[1],
           stream->tuple.ip_dst[2], stream->tuple.ip_dst[3],
          ntohs (stream->tuple.port_src), ntohs(stream->tuple.port_dst),ssrc);
  }
  //推迟到发流时再发建联消息
  // onNewIpcStreamFunc(p);
  if (p->capability_info.sps_len > 0) {
        printf("p->capability_info.sps_len len %d\n",p->capability_info.sps_len);
    new_nalu_fn(
        p->stream_id, p->seq, reinterpret_cast<struct ipc_nalu_t*>(p->capability_info.sps), p->capability_info.sps_len, user_);
    p->seq++;
  }
  if (p->capability_info.pps_len > 0) {
    printf("capability_info.pps len %d\n",p->capability_info.pps_len);
    new_nalu_fn(
        p->stream_id, p->seq, reinterpret_cast<struct ipc_nalu_t*>(p->capability_info.pps), p->capability_info.pps_len, user_);
    p->seq++;
  }
}

std::shared_ptr<ipc_stream_info> ipc_decoder::creatIpcStrem(uint64_t stream_id, flow_info* stream) {
  auto p = std::make_shared<ipc_stream_info>(stream);
  if (p == nullptr) {
    ipc_logger.Debug("std::make_shared<ipc_stream_info>()");
    return NULL;
  }
  p->flag = this->cfg_->flag;
  p->stream_id = stream_id;
  nalu_stream_map[stream_id] = p;
  return p;
}

std::shared_ptr<ipc_stream_info> ipc_decoder::findCreatIpcStrem(void* protoStream, int64_t flow_ssrc) {
  flow_info*         stream = static_cast<flow_info*>(protoStream);
  struct five_tuple* key = &stream->tuple;

  uint64_t stream_id = getStreamidFromTuple(key, flow_ssrc);
  auto     it = nalu_stream_map.find(stream_id);
  //第一次建立stream
  if (it == nalu_stream_map.end()) {
    printf("create ip stream id %u\n" ,stream_id);
    auto p = creatIpcStrem(stream_id, stream);
  }

  return nalu_stream_map[stream_id];
}

//暂时只接受RTPStream传入
int ipc_decoder::onNaluCallback(NaluType_e naluType, uint8_t* nalu, int naluLen, void* protoStream) {
  // printf("RTP onNaluCallback len %d\n",naluLen);

  RtpStream* stream = static_cast<RtpStream*>(protoStream);
  auto       it = findCreatIpcStrem(protoStream, stream->ssrc);
  if(it->seq != 0 && it->capability_info.proto_type == IPC_PROTOCOL_UNKNOWN && stream->MediaType != RtpMediaType::unknown ){
    it->seq = 0;
    it->capability_info.proto_type = stream->real_protocol_id;
  }
  // 存储当前RTP时间戳
  it->current_rtp_timestamp = stream->current_rtp_timestamp;
  if (global_logger.logLevel == LogLevel::Warning) {
    printf(
        "onNaluCallback RTP ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d %d  ssrc: %x\n",
        stream->tuple.ip_src[0], stream->tuple.ip_src[1],
        stream->tuple.ip_src[2], stream->tuple.ip_src[3],
        stream->tuple.ip_dst[0], stream->tuple.ip_dst[1],
        stream->tuple.ip_dst[2], stream->tuple.ip_dst[3],
     ntohs  ( stream->tuple.port_src),ntohs( stream->tuple.port_dst),stream->ssrc);
  }
  onNewIpcStreamFunc(it);

  if ((it->flag & IPC_DECODER_FLAG_AUDIO) == IPC_DECODER_FLAG_AUDIO) {
    if (stream->MediaType != RtpMediaType::audio) {
      return 0;
    }
  } else if ((it->flag & IPC_DECODER_FLAG_AUDIO) == IPC_DECODER_FLAG_AUDIO) {
    if (stream->MediaType == RtpMediaType::unknown || stream->MediaType == RtpMediaType::audio) {
      return 0;
    }
  }
  if (naluType == NT_SPS && naluLen <1024) {
    it->setSPS(nalu, naluLen);
  } else if (naluType == NT_PPS && naluLen <1024) {
    it->setPPS(nalu, naluLen);
  }

  new_nalu_fn(it->stream_id, it->seq, reinterpret_cast<struct ipc_nalu_t*>(nalu), naluLen, user_);
  it->seq++;
  return 0;
}

//暂时只接受DHAV传入
int ipc_decoder::onNaluCallback(NaluType_e naluType, uint8_t* nalu, int naluLen, flow_info* flow_info) {
  // printf("DHAV onNaluCallback len %d\n",naluLen);
  auto it = findCreatIpcStrem(flow_info, -1);

  struct ipc_capability_t capability;
  capability.encodeType_video = IPC_AVC_H264;
  capability.proto_type = IPC_PROTOCOL_DHAV;
  setIpcStreamCapability(it->stream_id, capability);
  if (global_logger.logLevel == LogLevel::Warning) {

    printf(
        "onNaluCallback DHAV ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d %d \n",
        flow_info->tuple.ip_src[0], flow_info->tuple.ip_src[1],
        flow_info->tuple.ip_src[2], flow_info->tuple.ip_src[3],
        flow_info->tuple.ip_dst[0], flow_info->tuple.ip_dst[1],
        flow_info->tuple.ip_dst[2], flow_info->tuple.ip_dst[3],
       ntohs (flow_info->tuple.port_src),ntohs( flow_info->tuple.port_dst));
  }
  onNewIpcStreamFunc(it);
  new_nalu_fn(it->stream_id, it->seq, reinterpret_cast<struct ipc_nalu_t*>(nalu), naluLen, user_);
  it->seq++;
  return 0;
}
