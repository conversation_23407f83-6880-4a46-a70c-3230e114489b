
#ifndef _SLC_RTSP_H
#define _SLC_RTSP_H

#include <map>
#include <functional>
#include <netinet/in.h>
#include <vector>
#include <memory>
#include <cstring>

#include "slc_register.h"
#include "slc_observer.h"
#include "slc_singleton.h"
#include "slc_common.h"
#include "slc_sdp.h"

#define STRLEN_CONST(str) (sizeof(str) - 1)
#define RTSP_FRAMEHDR ('$')

#define RTSP_MEDIA_PAYLOAD_LEN_MAX 128
#define RTSP_SDP_MEDIA_MAX_NUM 2
#define RTSP_SDP_M_ATTR_MAX_NUM 16

#define RTSP_SDP_MEDIA_MAX_LEN 128
#define RTSP_SDP_NUM1_BYTES_LEN 64
#define RTSP_SDP_NUM2_BYTES_LEN 128
#define RTSP_SDP_NUM3_BYTES_LEN 256

static const char *rtsp_methods[] = {"DESCRIBE", "ANNOUNCE", "GET_PARAMETER", "OPTIONS", "PAUSE", "PLAY", "RECORD", "REDIRECT",
    "SETUP", "SET_PARAMETER", "TEARDOWN"};

#define RTSP_NMETHODS array_length(rtsp_methods)
typedef enum {
  EM_RTSP_H_HEADER,
  EM_RSTP_H_ACCEPT,
  EM_RSTP_H_ACCEPT_CHARSET,
  EM_RSTP_H_ACCEPT_ENCODING,
  EM_RSTP_H_ACCEPT_LANGUAGE,
  EM_RSTP_H_ALLOW,
  EM_RSTP_H_ARCHIVEVIEWINGLIMITATION,
  EM_RSTP_H_ARCHIVEVIEWINGLIMITATION2,
  EM_RSTP_H_AUTHORIZATION,
  EM_RSTP_H_BANDWIDTH,
  EM_RSTP_H_BLOCKSIZE,
  EM_RSTP_H_CACHE_CONTROL,
  EM_RSTP_H_CONFERENCE,
  EM_RSTP_H_CONNECTION,
  EM_RSTP_H_CONTENT_BASE,
  EM_RSTP_H_CONTENT_ENCODING,
  EM_RSTP_H_CONTENT_LANGUAGE,
  EM_RSTP_H_CONTENT_LENGTH,
  EM_RSTP_H_CONTENT_LOCATION,
  EM_RSTP_H_CONTENT_TYPE,
  EM_RSTP_H_COOKIE,
  EM_RSTP_H_CSEQ,
  EM_RSTP_H_DATE,
  EM_RSTP_H_EXPIRES,
  EM_RSTP_H_FROM,
  EM_RSTP_H_IF_MATCH,
  EM_RSTP_H_IF_MODIFIED_SINCE,
  EM_RSTP_H_LAST_MODIFIED,
  EM_RSTP_H_LOCATION,
  EM_RSTP_H_PROXY_AUTHENTICATE,
  EM_RSTP_H_PROXY_REQUIRE,
  EM_RSTP_H_PUBLIC,
  EM_RSTP_H_RANGE,
  EM_RSTP_H_REFERER,
  EM_RSTP_H_RETRY_AFTER,
  EM_RSTP_H_REQUIRE,
  EM_RSTP_H_RTP_INFO,
  EM_RSTP_H_SCALE,
  EM_RSTP_H_SPEED,
  EM_RSTP_H_SERVER,
  EM_RSTP_H_SESSION,
  EM_RSTP_H_SUPPORTED,
  EM_RSTP_H_TIMESTAMP,
  EM_RSTP_H_TRANSPORT,
  EM_RSTP_H_UNSUPPORTED,
  EM_RSTP_H_USER_AGENT,
  EM_RSTP_H_VARY,
  EM_RSTP_H_VIA,
  EM_RSTP_H_WWW_AUTHENTICATE,
  EM_RTSP_H_MAX
} rtsp_field;
struct rtsp_hdst_data {
  uint8_t     index;
  const char *head_type;
};

static struct rtsp_hdst_data rtsp_header_data[] = {{EM_RTSP_H_HEADER, "header"}, {EM_RSTP_H_ACCEPT, "accept"},
    {EM_RSTP_H_ACCEPT_CHARSET, "accept-charset"}, {EM_RSTP_H_ACCEPT_ENCODING, "accept-encoding"},
    {EM_RSTP_H_ACCEPT_LANGUAGE, "accept-language"}, {EM_RSTP_H_ALLOW, "allow"},
    {EM_RSTP_H_ARCHIVEVIEWINGLIMITATION, "archiveViewingLimitation"},
    {EM_RSTP_H_ARCHIVEVIEWINGLIMITATION2, "archiveViewingLimitation2"}, {EM_RSTP_H_AUTHORIZATION, "authorization"},
    {EM_RSTP_H_BANDWIDTH, "bandwidth"}, {EM_RSTP_H_BLOCKSIZE, "blocksize"}, {EM_RSTP_H_CACHE_CONTROL, "cache-control"},
    {EM_RSTP_H_CONFERENCE, "conference"}, {EM_RSTP_H_CONNECTION, "connection"}, {EM_RSTP_H_CONTENT_BASE, "content-base"},
    {EM_RSTP_H_CONTENT_ENCODING, "content-encoding"}, {EM_RSTP_H_CONTENT_LANGUAGE, "content-language"},
    {EM_RSTP_H_CONTENT_LENGTH, "content-length"}, {EM_RSTP_H_CONTENT_LOCATION, "content-location"},
    {EM_RSTP_H_CONTENT_TYPE, "content-type"}, {EM_RSTP_H_COOKIE, "cookie"}, {EM_RSTP_H_CSEQ, "cseq"}, {EM_RSTP_H_DATE, "date"},
    {EM_RSTP_H_EXPIRES, "expires"}, {EM_RSTP_H_FROM, "from"}, {EM_RSTP_H_IF_MATCH, "if-match"},
    {EM_RSTP_H_IF_MODIFIED_SINCE, "if-modified-since"}, {EM_RSTP_H_LAST_MODIFIED, "last-modified"},
    {EM_RSTP_H_LOCATION, "location"}, {EM_RSTP_H_PROXY_AUTHENTICATE, "proxy-authenticate"},
    {EM_RSTP_H_PROXY_REQUIRE, "proxy-require"}, {EM_RSTP_H_PUBLIC, "public"}, {EM_RSTP_H_RANGE, "range"},
    {EM_RSTP_H_REFERER, "referer"}, {EM_RSTP_H_RETRY_AFTER, "retry-after"}, {EM_RSTP_H_REQUIRE, "require"},
    {EM_RSTP_H_RTP_INFO, "rtp-info"}, {EM_RSTP_H_SCALE, "scale"}, {EM_RSTP_H_SPEED, "speed"}, {EM_RSTP_H_SERVER, "server"},
    {EM_RSTP_H_SESSION, "session"}, {EM_RSTP_H_SUPPORTED, "supported"}, {EM_RSTP_H_TIMESTAMP, "timestamp"},
    {EM_RSTP_H_TRANSPORT, "transport"}, {EM_RSTP_H_UNSUPPORTED, "unsupported"}, {EM_RSTP_H_USER_AGENT, "user-Agent"},
    {EM_RSTP_H_VARY, "vary"}, {EM_RSTP_H_VIA, "via"}, {EM_RSTP_H_WWW_AUTHENTICATE, "www-authenticate"}};

static const value_string rtsp_rtp_type_vals[] = {
    /*    0 */ {PT_PCMU, "ITU-T G.711 PCMU"},
    /*    1 */ {PT_1016, "USA Federal Standard FS-1016"},
    /*    2 */ {PT_G721, "ITU-T G.721"},
    /*    3 */ {PT_GSM, "GSM 06.10"},
    /*    4 */ {PT_G723, "ITU-T G.723"},
    /*    5 */ {PT_DVI4_8000, "DVI4 8000 samples/s"},
    /*    6 */ {PT_DVI4_16000, "DVI4 16000 samples/s"},
    /*    7 */ {PT_LPC, "Experimental linear predictive encoding from Xerox PARC"},
    /*    8 */ {PT_PCMA, "ITU-T G.711 PCMA"},
    /*    9 */ {PT_G722, "ITU-T G.722"},
    /* 10 */ {PT_L16_STEREO, "16-bit uncompressed audio, stereo"},
    /* 11 */ {PT_L16_MONO, "16-bit uncompressed audio, monaural"},
    /* 12 */ {PT_QCELP, "Qualcomm Code Excited Linear Predictive coding"},
    /* 13 */ {PT_CN, "Comfort noise"},
    /* 14 */ {PT_MPA, "MPEG-I/II Audio"},
    /* 15 */ {PT_G728, "ITU-T G.728"},
    /* 16 */ {PT_DVI4_11025, "DVI4 11025 samples/s"},
    /* 17 */ {PT_DVI4_22050, "DVI4 22050 samples/s"},
    /* 18 */ {PT_G729, "ITU-T G.729"},
    /* 19 */ {PT_CN_OLD, "Comfort noise (old)"},
    /* 20 */ {20, "Unassigned"},
    /* 21 */ {21, "Unassigned"},
    /* 22 */ {22, "Unassigned"},
    /* 23 */ {23, "Unassigned"},
    /* 24 */ {24, "Unassigned"},
    /* 25 */ {PT_CELB, "Sun CellB video encoding"},
    /* 26 */ {PT_JPEG, "JPEG-compressed video"},
    /* 27 */ {27, "Unassigned"},
    /* 28 */ {PT_NV, "'nv' program"},
    /* 29 */ {29, "Unassigned"},
    /* 30 */ {30, "Unassigned"},
    /* 31 */ {PT_H261, "ITU-T H.261"},
    /* 32 */ {PT_MPV, "MPEG-I/II Video"},
    /* 33 */ {PT_MP2T, "MPEG-II transport streams"},
    /* 34 */ {PT_H263, "ITU-T H.263"},
    /* 35-71     Unassigned  */
    /* 35 */ {35, "Unassigned"},
    /* 36 */ {36, "Unassigned"},
    /* 37 */ {37, "Unassigned"},
    /* 38 */ {38, "Unassigned"},
    /* 39 */ {39, "Unassigned"},
    /* 40 */ {40, "Unassigned"},
    /* 41 */ {41, "Unassigned"},
    /* 42 */ {42, "Unassigned"},
    /* 43 */ {43, "Unassigned"},
    /* 44 */ {44, "Unassigned"},
    /* 45 */ {45, "Unassigned"},
    /* 46 */ {46, "Unassigned"},
    /* 47 */ {47, "Unassigned"},
    /* 48 */ {48, "Unassigned"},
    /* 49 */ {49, "Unassigned"},
    /* 50 */ {50, "Unassigned"},
    /* 51 */ {51, "Unassigned"},
    /* 52 */ {52, "Unassigned"},
    /* 53 */ {53, "Unassigned"},
    /* 54 */ {54, "Unassigned"},
    /* 55 */ {55, "Unassigned"},
    /* 56 */ {56, "Unassigned"},
    /* 57 */ {57, "Unassigned"},
    /* 58 */ {58, "Unassigned"},
    /* 59 */ {59, "Unassigned"},
    /* 60 */ {60, "Unassigned"},
    /* 61 */ {61, "Unassigned"},
    /* 62 */ {62, "Unassigned"},
    /* 63 */ {63, "Unassigned"},
    /* 64 */ {64, "Unassigned"},
    /* 65 */ {65, "Unassigned"},
    /* 66 */ {66, "Unassigned"},
    /* 67 */ {67, "Unassigned"},
    /* 68 */ {68, "Unassigned"},
    /* 69 */ {69, "Unassigned"},
    /* 70 */ {70, "Unassigned"},
    /* 71 */ {71, "Unassigned"},
    /* 72-76     Reserved for RTCP conflict avoidance                                   [RFC3551] */
    /* 72 */ {72, "Reserved for RTCP conflict avoidance"},
    /* 73 */ {73, "Reserved for RTCP conflict avoidance"},
    /* 74 */ {74, "Reserved for RTCP conflict avoidance"},
    /* 75 */ {75, "Reserved for RTCP conflict avoidance"},
    /* 76 */ {76, "Reserved for RTCP conflict avoidance"},
    /* 77-95     Unassigned      ? */
    /* 77 */ {77, "Unassigned"},
    /* 78 */ {78, "Unassigned"},
    /* 79 */ {79, "Unassigned"},
    /* 80 */ {80, "Unassigned"},
    /* 81 */ {81, "Unassigned"},
    /* 82 */ {82, "Unassigned"},
    /* 83 */ {83, "Unassigned"},
    /* 84 */ {84, "Unassigned"},
    /* 85 */ {85, "Unassigned"},
    /* 86 */ {86, "Unassigned"},
    /* 87 */ {87, "Unassigned"},
    /* 88 */ {88, "Unassigned"},
    /* 89 */ {89, "Unassigned"},
    /* 90 */ {90, "Unassigned"},
    /* 91 */ {91, "Unassigned"},
    /* 92 */ {92, "Unassigned"},
    /* 93 */ {93, "Unassigned"},
    /* 94 */ {94, "Unassigned"},
    /* 95 */ {95, "Unassigned"},
    /* Added to support addtional RTP payload types
         * See epan/rtp_pt.h */
    {PT_UNDF_96, "DynamicRTP-Type-96"},
    {PT_UNDF_97, "DynamicRTP-Type-97"},
    {PT_UNDF_98, "DynamicRTP-Type-98"},
    {PT_UNDF_99, "DynamicRTP-Type-99"},
    {PT_UNDF_100, "DynamicRTP-Type-100"},
    {PT_UNDF_101, "DynamicRTP-Type-101"},
    {PT_UNDF_102, "DynamicRTP-Type-102"},
    {PT_UNDF_103, "DynamicRTP-Type-103"},
    {PT_UNDF_104, "DynamicRTP-Type-104"},
    {PT_UNDF_105, "DynamicRTP-Type-105"},
    {PT_UNDF_106, "DynamicRTP-Type-106"},
    {PT_UNDF_107, "DynamicRTP-Type-107"},
    {PT_UNDF_108, "DynamicRTP-Type-108"},
    {PT_UNDF_109, "DynamicRTP-Type-109"},
    {PT_UNDF_110, "DynamicRTP-Type-110"},
    {PT_UNDF_111, "DynamicRTP-Type-111"},
    {PT_UNDF_112, "DynamicRTP-Type-112"},
    {PT_UNDF_113, "DynamicRTP-Type-113"},
    {PT_UNDF_114, "DynamicRTP-Type-114"},
    {PT_UNDF_115, "DynamicRTP-Type-115"},
    {PT_UNDF_116, "DynamicRTP-Type-116"},
    {PT_UNDF_117, "DynamicRTP-Type-117"},
    {PT_UNDF_118, "DynamicRTP-Type-118"},
    {PT_UNDF_119, "DynamicRTP-Type-119"},
    {PT_UNDF_120, "DynamicRTP-Type-120"},
    {PT_UNDF_121, "DynamicRTP-Type-121"},
    {PT_UNDF_122, "DynamicRTP-Type-122"},
    {PT_UNDF_123, "DynamicRTP-Type-123"},
    {PT_UNDF_124, "DynamicRTP-Type-124"},
    {PT_UNDF_125, "DynamicRTP-Type-125"},
    {PT_UNDF_126, "DynamicRTP-Type-126"},
    {PT_UNDF_127, "DynamicRTP-Type-127"},

    {0, NULL},
};

typedef enum {
  RTSP_UNKNOWN,
  RTSP_REQUEST,
  RTSP_REPLY,
  RTSP_NOT_FIRST_LINE,
  RTSP_RTP_OVER,
  RTSP_MESSAGE,
} rtsp_type_t;

struct rtsp_point_value {
  uint16_t       len;
  const uint8_t *ptr;
};

struct rtsp_sdp_m_info {
  //char m_media[RTSP_SDP_NUM3_BYTES_LEN];

  char m_type[RTSP_SDP_NUM1_BYTES_LEN];
  char m_port[RTSP_SDP_NUM1_BYTES_LEN];
  char m_proto[RTSP_SDP_NUM1_BYTES_LEN];
  //char m_title[RTSP_SDP_NUM1_BYTES_LEN];
  char m_payloads[RTSP_SDP_MEDIA_MAX_LEN];
  char a_attributes[RTSP_SDP_M_ATTR_MAX_NUM][256];
};

typedef struct _rtsp_sdp_info_t {
  char v_version[RTSP_SDP_NUM1_BYTES_LEN];

  //char o_owner[RTSP_SDP_NUM3_BYTES_LEN];
  char o_username[RTSP_SDP_NUM1_BYTES_LEN];
  char o_sessionid[RTSP_SDP_NUM1_BYTES_LEN];
  char o_version[RTSP_SDP_NUM1_BYTES_LEN];
  char o_network_type[RTSP_SDP_NUM1_BYTES_LEN];
  char o_address_type[RTSP_SDP_NUM1_BYTES_LEN];
  char o_address[RTSP_SDP_NUM1_BYTES_LEN];

  char s_name[RTSP_SDP_NUM1_BYTES_LEN];
  char i_info[RTSP_SDP_NUM1_BYTES_LEN];
  char u_uri[RTSP_SDP_NUM1_BYTES_LEN];
  char e_email[RTSP_SDP_NUM1_BYTES_LEN];
  char p_phone[RTSP_SDP_NUM1_BYTES_LEN];

  //char c_info[RTSP_SDP_NUM3_BYTES_LEN];
  char c_network_type[RTSP_SDP_NUM1_BYTES_LEN];
  char c_address_type[RTSP_SDP_NUM1_BYTES_LEN];
  char c_address[RTSP_SDP_NUM1_BYTES_LEN];

  char b_bandwidths[RTSP_SDP_NUM1_BYTES_LEN];

  //char t_time[RTSP_SDP_NUM2_BYTES_LEN];
  char t_time_start[RTSP_SDP_NUM1_BYTES_LEN];
  char t_time_end[RTSP_SDP_NUM1_BYTES_LEN];

  char r_repeat_interval[RTSP_SDP_NUM1_BYTES_LEN];
  char r_active_duration[RTSP_SDP_NUM1_BYTES_LEN];
  char r_offsets_from_start_time[RTSP_SDP_NUM1_BYTES_LEN];

  char z_timezone_adjustment[RTSP_SDP_NUM1_BYTES_LEN];
  char k_encryptionkey[RTSP_SDP_NUM1_BYTES_LEN];

  char session_attribute[21][256];

  struct rtsp_sdp_m_info m_info[RTSP_SDP_MEDIA_MAX_NUM];

} rtsp_sdp_info_t;

typedef struct rtsp_interleaved_frame_t {
  uint8_t  magic;
  uint8_t  channel;
  uint16_t len;

} RTSPInterleavedFrame;

class RtspStream : public flow_info {
public:
  RtspStream(flow_info *f_info) : flow_info(*f_info) {
    transport_ssrc_count = 0;
    memset(transport_ssrc, 0, sizeof(transport_ssrc));
  }

  int enqueRtspPayload(uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
  int dissect_rtspmessage(const uint8_t *payload, const uint32_t payload_len);
  int dissect_rtp_over_rtsp(const uint8_t *payload, const uint32_t payload_len);

  int            get_rtsp_len(const uint8_t *payload, int payload_len, rtsp_type_t *type);
  int            is_rtsp_message(const uint8_t *payload, int payload_len);
  int            is_rtp_over_rtsp(const uint8_t *payload, int payload_len);
  int64_t        get_header_len(const uint8_t *p, int l);
  int64_t        get_contentlen(const uint8_t *p, int l);
  const uint8_t *rtsp_parse_line_info(const uint8_t *payload, const uint16_t payload_len);
  uint8_t        is_rtsp_request_or_reply(const uint8_t *line, int linelen, rtsp_type_t *type);
  void           process_rtsp_request(const uint8_t *line, int linelen);
  int            process_rtsp_reply_transport(const uint8_t *line, int linelen);
  void           process_rtsp_reply(const uint8_t *line, int linelen);
  int            dissect_rtsp_sdp(const uint8_t *payload, const uint32_t payload_len, rtsp_sdp_info_t *info);

  void dissect_rtsp_conversation();
  void createSsrcMediaBinding();

  // SSRC 数组相关的辅助函数
  uint32_t getSsrc(int index = 0) const;  // 获取指定索引的 SSRC，默认获取第一个
  int getSsrcCount() const;               // 获取 SSRC 数量
  void clearSsrcs();                      // 清空 SSRC 数组

public:
  char     sps_[1024] = {0};
  int      sps_len_ = 0;
  char     pps_[1024] = {0};
  int      pps_len_ = 0;
  SdpInfo  sdp_;

private:
  std::vector<uint8_t>    cache[2];
  uint32_t                version_;
  char                    request_method_[20];
  const uint8_t          *request_uri_;
  uint16_t                request_uri_len_;
  uint32_t                status_code_;
  const uint8_t          *response_code_;
  uint16_t                response_code_len_;
  rtsp_sdp_info_t         sdp_info_ = {0};
  GHashTable             *table_;
  struct rtsp_point_value rstp_head_[EM_RTSP_H_MAX] = {0};
  uint8_t                 transport_info_parsed_ = 0; /* 是否解析到 transport 信息 */
  uint8_t                 rtp_over_rtsp_parsed_ = 0;  /* 是否解析到 transport 信息 */
  uint8_t                 media_mapping_created_ = 0; /* 是否已创建媒体映射，避免重复处理 */
  char                    transport_client_port_[12]; /* eg: 54782-54783 */
  char transport_server_port_[12];                    /* eg: 45020-45021 */
  uint32_t transport_ssrc[12];
  int      transport_ssrc_count = 0;  // 记录当前存储的SSRC数量
};
#define RTSPKEEPER RtspKeeper::GetInstance()

class RtspKeeper : public ObserverSessionKeeper, public singleton<RtspKeeper> {
  friend class SlcProtoLoader<RtspKeeper>;

public:
  RtspKeeper();
  int  dissectProto_rsm(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
  int  miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len) { return 0; }
  void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);

  std::shared_ptr<RtspStream> findRtspStreamByFlow(flow_info *flow) {
    auto it = map_streams_.find(flow);
    if (it != map_streams_.end()) {
      return it->second;
    }
    return NULL;
  }

private:
  std::map<flow_info *, std::shared_ptr<RtspStream>> map_streams_;
};

#endif