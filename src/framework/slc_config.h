/****************************************************************************************
 * 文 件 名 : slc_config.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-03-17
* 编    码 : root      '2018-03-17
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _SLC_CONFIG_H_
#define _SLC_CONFIG_H_

#include "ipc/ipc.h"
#include <common/c_lang_linkage_start.h>
#include <iniparser/iniparser.h>
#include <common/c_lang_linkage_end.h>

#include <string>
#include <arpa/inet.h>
#include <iostream>
#include <sstream>
#define SLC_CONF_PATH "./config.ini"
typedef const char *CSTR;

#define CFG SlcConfig::GetInstance()
#define RUNMODE CFG->GetValueOf<int>("MODE", 0)
#define INTERFACE_NAME CFG->GetValueOf<std::string>("IF").c_str()
#define PCAP_FILE_DIR CFG->GetValueOf<std::string>("PCAP_FILES_DIR").c_str()
#define SLC_RTP_SENDTO_PORT CFG->GetValueOf<int>("SLC_RTP_SENDTO_PORT", 1234)
#define ARM_DEVICE CFG->GetValueOf<int>("ARM_DEVICE", 1)
#define LOG_LEVEL CFG->GetValueOf<int>("LOG_LEVEL", 0)
#define RTSP_SERVER_IP CFG->GetValueOf<std::string>("RTSP_SERVER_IP").c_str()
#define TCP_RSM_OUT_OF_ORDER CFG->GetValueOf<int>("TCP_RSM_OUT_OF_ORDER", 5)

class SlcConfig {
public:
  static SlcConfig *GetInstance();

  SlcConfig() {}  // 运行在so接口模式下 提供一个默认的构造函数
  SlcConfig(const std::string &strConfigFilePath) : strConfigFile_(strConfigFilePath) {
    const char *ini_name = strConfigFile_.c_str();
    ini_ = iniparser_load(ini_name);
    if (ini_ == NULL) {
      fprintf(stderr, "cannot parse file: %s\n", ini_name);
      return;
    }
  }
  ~SlcConfig() { iniparser_freedict(ini_); }

public:
  template <typename T>
  T GetValueOf(const std::string &strConfigName, T t = T()) {
    if (nullptr == ini_) {
      return t;
    }

    return GetValueOf_inner<T>(toGlobalConfigName(strConfigName), t);
  }

  int parse();

  enum {
    eMode_OfflinePcap = 0,
    eMode_LiveCap = 1,
  };
  enum {
    eMode_RtpUnpack = 0,
    eMode_RtpTransfer = 1,
  };

  uint32_t getCameraIpAddr() { return excludeIP_; }
  int      getRtpTransferMode() { return rtpTransferMode_; }
  int      getCameraRtspPort() { return cameraRtspPort_; }

  void             setExcludeIP( char *pStrValue ) {

      excludeIP_ = ntohl(inet_addr(pStrValue));
   }

  void             setgetFrameType(IPC_FRAME_TYPE_E type) { arm_device_ = type; }
  IPC_FRAME_TYPE_E getFrameType() { return arm_device_; }

private:
  const std::string toGlobalConfigName(const std::string &strConfigName);
  template <typename T>
  T GetValueOf_inner(const std::string &strConfigName, T def);

private:
  int         lWorkMode_;
  std::string strIfName_;
  std::string strPcapFileDir_;
  std::string strTblDir_;
  std::string strFieldsDir_;
  std::string strSlcCapFilter;

  std::string      strConfigFile_;
  uint32_t         excludeIP_;
  dictionary      *ini_;
  int              rtpTransferMode_;
  int              cameraRtspPort_;
  IPC_FRAME_TYPE_E arm_device_ = IPC_NETWORK_ETH;
};

#endif /* _SLC_CONFIG_H_ */
