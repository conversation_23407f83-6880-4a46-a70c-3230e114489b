cmake_minimum_required(VERSION 3.15)

set(ProName "yaslc")
project(${ProName} LANGUAGES C CXX)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDRAD 11)

# 获取 gcc 版本
execute_process(
  COMMAND ${CMAKE_C_COMPILER} -dumpversion
  OUTPUT_VARIABLE GCC_VERSION
  OUTPUT_STRIP_TRAILING_WHITESPACE
)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

message(STATUS "gcc version ${GCC_VERSION}")

#  版本设置
if (EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/VERSION")
  file(READ "${CMAKE_CURRENT_SOURCE_DIR}/VERSION" PROGRAM_VERSION)
  string(STRIP "${PROGRAM_VERSION}" PROGRAM_VERSION)
  string(TIMESTAMP PROJECT_VERSION_TWEAK "%Y%m%d")
else()
  message(FATAL_ERROR "FILE ${CMAKE_CURRENT_SOURCE_DIR}/VERSION not found")
endif()

message("program version ${PROGRAM_VERSION}")
message("patch verson ${PROJECT_VERSION_TWEAK}")

# 生成 version.h
configure_file(
  cmake/version.h.in
  include/version.h
  @ONLY
)
set(CMAKE_BUILD_TYPE "Debug")
set(AppName "${ProName}")
set(YA_PROJECT_LIBRARY "ipc")

set(slc_files "../etc/config.ini")
set(slc_dirs "")
set(slc_bins "")

# 适配 旧版 makefile
add_definitions(-DPROJECT_VERSION_STR="${PROGRAM_VERSION}")

set(CMAKE_CXX_FLAGS "-O0 -g")
set(CMAKE_C_FLAGS "-O0 -g")

# 是否生成 compile_command.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

option(EMABLE_MEMORY_CHECK "Enable memory check" ON)
option(USE_CLANG_TIDY  "Use clang-tidy for static analysis" OFF)
option(BUILD_TESTS "Build test programs" OFF)

if (USE_CLANG_TIDY)
  find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
  if (CLANG_TIDY_EXE)
    set(CMAKE_CXX_CLANG_TIDY "${CLANG_TIDY_EXE}")
    set(CMAKE_C_CLANG_TIDY "${CLANG_TIDY_EXE}")
  else()
    message(WARNING "clang-tidy not found")
  endif()
endif()


include(GNUInstallDirs)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY  ${CMAKE_SOURCE_DIR}/lib)
if (CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
  set(CMAKE_INSTALL_PREFIX "/root/program" CACHE PATH "..." FORCE)
endif()
message(STATUS "install prefix: ${CMAKE_INSTALL_PREFIX}")

set(INSTALL_LIBDIR ${CMAKE_PROJECT_NAME}/${CMAKE_INSTALL_LIBDIR} CACHE PATH "Installation directory for libraries")
set(INSTALL_BINDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for executables")
set(INSTALL_INCLUDEDIR ${CMAKE_PROJECT_NAME}/${CMAKE_INSTALL_INCLUDEDIR} CACHE PATH "Installation directory for header files")
set(INSTALL_ROOTDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for files")
set(INSTALL_CMAKEDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for header files")

# 报告安装位置
message(STATUS "Project will be installed to ${CMAKE_INSTALL_PREFIX}")
foreach(p LIB BIN INCLUDE CMAKE ROOT)
  file(TO_NATIVE_PATH ${CMAKE_INSTALL_PREFIX}/${INSTALL_${p}DIR} _path)
  message(STATUS "Installing ${p} components to ${_path}")
  unset(_path)
endforeach()



# set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")

# 检测编译器类型
if(CMAKE_C_COMPILER MATCHES "arm-openwrt-linux-muslgnueabi-gcc")
    set(IS_ARM_OPENWRT TRUE)
    message(STATUS "Detected ARM OpenWrt compiler")
else()
    set(IS_ARM_OPENWRT FALSE)
    message(STATUS "Using default GCC compiler")
endif()

add_subdirectory(src)

# 根据选项决定是否编译test目录
add_subdirectory(test)
if(BUILD_TESTS)
    add_subdirectory(http_server)
    message(STATUS "Building test programs")
else()
    message(STATUS "Skipping test programs")
endif()

#
# test
#